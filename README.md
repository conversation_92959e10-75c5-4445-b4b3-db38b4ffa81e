# Audit Disk Script

A comprehensive disk usage monitoring system that tracks large files across multiple filesystems, monitors growth patterns over time, and sends email alerts when predefined thresholds are exceeded.

## Features

- **Comprehensive Monitoring**: Scans all mounted filesystems for large files
- **Growth Pattern Detection**: Tracks file growth over time with configurable thresholds
- **Multiple Alert Types**: 
  - Sustained growth pattern alerts
  - 24-hour rapid growth alerts
  - 7-day sustained growth alerts
  - Low disk space alerts
- **Email Notifications**: HTML-formatted email alerts with detailed tables
- **Interactive & Automated Modes**: Suitable for both manual execution and cron automation
- **Security**: Path validation, email sanitization, and permission checks
- **Performance**: Parallel scanning, timeouts, and configurable depth limits

## Quick Start

### Basic Usage

```bash
# Interactive mode - scan all filesystems
./audit_disk.sh

# Automated mode - for cron with email alerts
./audit_disk.sh -audit

# Monitor specific directory
./audit_disk.sh -dir /var/log

# Display help
./audit_disk.sh --help

# Show current configuration
./audit_disk.sh --config

# Test email functionality
./audit_disk.sh --test-email
```

### Cron Setup

Add to crontab for hourly monitoring:

```bash
# Hourly automated monitoring
0 * * * * /path/to/audit_disk.sh -audit
```

## Configuration

Edit the configuration variables at the top of the script:

### File Size Thresholds
- `LARGE_FILE_THRESHOLD_MB=250` - Minimum file size to track (MB)
- `GROWTH_PATTERN_THRESHOLD_MB=150` - Cumulative growth for pattern alerts (MB)
- `DAILY_GROWTH_THRESHOLD_MB=300` - 24-hour growth threshold (MB)
- `WEEKLY_GROWTH_THRESHOLD_MB=500` - 7-day growth threshold (MB)

### Disk Space Thresholds
- `DISK_SPACE_ALERT_GB=10` - Low disk space alert threshold (GB)
- `DISK_SPACE_CHANGE_GB=20` - Disk space decrease alert threshold (GB)

### Email Configuration
- `EMAIL_RECIPIENT="<EMAIL>"` - Alert recipient
- `EMAIL_SUBJECT_PREFIX="Disk Audit Alert"` - Email subject prefix
- `EMAIL_SPAM_PREVENTION_HOURS=6` - Minimum hours between low space alerts

### Data Management
- `DATA_RETENTION_DAYS=7` - CSV file retention period
- `DATA_DIR="${HOME}/.audit_disk"` - Data storage directory (user's home)

## Installation

### Prerequisites

```bash
# CentOS/RHEL
yum install mailx findutils coreutils gawk bc

# Ubuntu/Debian
apt-get install mailutils findutils coreutils gawk bc
```

### Installation Steps

1. **Copy script to system location:**
   ```bash
   sudo cp audit_disk.sh /usr/local/bin/
   sudo chmod +x /usr/local/bin/audit_disk.sh
   ```

2. **Create data directory:**
   ```bash
   sudo mkdir -p /var/lib/audit_disk
   sudo chmod 755 /var/lib/audit_disk
   ```

3. **Configure email settings** in the script

4. **Test email functionality:**
   ```bash
   /usr/local/bin/audit_disk.sh --test-email
   ```

5. **Setup cron job:**
   ```bash
   (crontab -l 2>/dev/null; echo "0 * * * * /usr/local/bin/audit_disk.sh -audit") | crontab -
   ```

## Data Storage

### File Structure
```
/var/lib/audit_disk/
├── audit_disk_2025-01-14.csv    # Daily CSV files
├── audit_disk_2025-01-15.csv
├── .state                       # State tracking file
├── .lock                        # Process lock file
└── audit_disk.log              # Log file (if verbose mode)
```

### CSV Format
```csv
timestamp,filepath,size_bytes,disk_free_bytes,mount_point,scan_type
2025-01-14T10:30:00,/var/log/messages,524288000,85899345920,/var,initial
2025-01-14T11:30:00,/var/log/messages,536870912,85887063040,/var,rescan
```

## Alert Types

### 1. Growth Pattern Alerts
- **Condition**: Files with ≥3 consecutive scans showing growth AND cumulative growth >150MB
- **Email**: Red background table with file details

### 2. 24-Hour Rapid Growth
- **Condition**: Files that grew >300MB in the last 24 hours
- **Email**: Orange background table with growth details

### 3. 7-Day Sustained Growth
- **Condition**: Files that grew >500MB over the last 7 days
- **Email**: Blue background table with weekly growth

### 4. Disk Space Alerts
- **Condition**: Available space <10GB OR decreased >20GB in 24h
- **Email**: Red background table with disk space details

## Command Line Options

| Option | Description |
|--------|-------------|
| (none) | Interactive mode - full filesystem scan with colored output |
| `-audit` | Automated mode - designed for cron, sends email alerts |
| `-dir <path>` | Directory-specific monitoring of specified path |
| `-v, --verbose` | Enable verbose logging to log file |
| `-h, --help` | Display help message |
| `--config` | Display current configuration settings |
| `--test-email` | Test email functionality |
| `--cleanup` | Perform maintenance cleanup of old data |

## Security Features

- **Path Validation**: Prevents path traversal attacks
- **Email Sanitization**: Prevents email header injection
- **File Permissions**: Restricted permissions on data files (600)
- **Lock Files**: Prevents multiple instances from running simultaneously
- **Input Validation**: Validates all configuration parameters

## Performance Features

- **Parallel Scanning**: Multiple filesystems scanned simultaneously
- **Timeouts**: Prevents hanging on network filesystems
- **Depth Limits**: Configurable directory traversal depth
- **Network FS Handling**: Optional skipping of network filesystems
- **Progress Indicators**: Real-time progress display in interactive mode

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Ensure data directory is writable
   sudo chown $(whoami) /var/lib/audit_disk
   ```

2. **Email Not Sending**
   ```bash
   # Test mailx configuration
   echo "Test" | mailx -s "Test" <EMAIL>
   ```

3. **Command Not Found**
   ```bash
   # Install missing dependencies
   sudo yum install mailx findutils coreutils gawk bc  # CentOS/RHEL
   sudo apt-get install mailutils findutils coreutils gawk bc  # Ubuntu/Debian
   ```

4. **Script Hanging**
   - Check network filesystem timeouts
   - Verify find command timeout settings
   - Review excluded filesystem configurations

### Log Analysis

Enable verbose logging:
```bash
./audit_disk.sh -v
tail -f /var/lib/audit_disk/audit_disk.log
```

## Exit Codes

| Code | Description |
|------|-------------|
| 0 | Normal completion |
| 1 | Configuration or parameter error |
| 2 | Insufficient permissions |
| 3 | Critical disk space condition |
| 4 | Email delivery failure |
| 5 | Data corruption or missing files |

## Version Information

- **Version**: 1.0
- **Author**: System Administration Team
- **Date**: 2025-01-14
- **Compatibility**: CentOS 7+, RHEL 7+, Ubuntu 18.04+
- **Bash Version**: 4.0 or higher

## License

This script is provided as-is for system administration purposes. Modify and distribute according to your organization's policies.
