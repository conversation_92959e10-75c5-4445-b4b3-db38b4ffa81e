#!/bin/bash

# =============================================================================
# Audit Disk Script - Comprehensive Disk Usage Monitoring
# =============================================================================
# Version: 1.0
# Author: System Administration Team
# Date: 2025-01-14
# Purpose: Monitor disk usage and file growth patterns across multiple filesystems
# =============================================================================

# =============================================================================
# CONFIGURATION SECTION
# =============================================================================

# File Size Thresholds (in MB)
LARGE_FILE_THRESHOLD_MB=250          # Initial scan minimum file size
GROWTH_PATTERN_THRESHOLD_MB=150      # Cumulative growth for pattern alerts
DAILY_GROWTH_THRESHOLD_MB=300        # 24-hour growth threshold
WEEKLY_GROWTH_THRESHOLD_MB=500       # 7-day growth threshold

# Disk Space Thresholds (in GB)
DISK_SPACE_ALERT_GB=10              # Low disk space alert threshold
DISK_SPACE_CHANGE_GB=20             # Disk space decrease alert threshold

# Data Management
DATA_RETENTION_DAYS=7               # CSV file retention period
CONSECUTIVE_GROWTH_COUNT=3          # Required consecutive growth scans

# Email Configuration
EMAIL_RECIPIENT="<EMAIL>"
EMAIL_SUBJECT_PREFIX="Disk Audit Alert"
EMAIL_SPAM_PREVENTION_HOURS=6       # Minimum hours between low space alerts

# System Paths
DATA_DIR="${HOME}/.audit_disk"      # Data storage directory (user's home)
STATE_FILE="$DATA_DIR/.state"       # State tracking file
LOG_FILE="$DATA_DIR/audit_disk.log" # Optional verbose logging
LOCK_FILE="$DATA_DIR/.lock"         # Process lock file

# Filesystem Exclusions
EXCLUDED_FILESYSTEMS="proc sys dev tmp run"
EXCLUDED_MOUNT_TYPES="tmpfs devtmpfs sysfs proc"

# Performance Tuning
MAX_FIND_DEPTH=10                   # Maximum directory traversal depth
FIND_TIMEOUT_SECONDS=3600           # Timeout for find operations
PARALLEL_SCAN_ENABLED=true          # Enable parallel filesystem scanning

# Network Filesystem Handling
NETWORK_FS_TIMEOUT=30               # Timeout for network filesystem operations
SKIP_NETWORK_FS=true                # Skip NFS, CIFS, etc.

# Output Formatting
COLOR_OUTPUT_ENABLED=true           # Enable color output in interactive mode
PROGRESS_INDICATORS=true            # Show progress during long operations
TABLE_WIDTH=120                     # Maximum table width for formatting

# Color Definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
ORANGE='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Exit Codes
EXIT_SUCCESS=0          # Normal completion
EXIT_CONFIG_ERROR=1     # Configuration or parameter error
EXIT_PERMISSION_ERROR=2 # Insufficient permissions
EXIT_DISK_CRITICAL=3    # Critical disk space condition
EXIT_EMAIL_ERROR=4      # Email delivery failure
EXIT_DATA_ERROR=5       # Data corruption or missing files

# Global Variables
SCRIPT_MODE="interactive"           # Default mode
VERBOSE=false                      # Verbose logging
TARGET_DIR=""                      # Directory-specific scanning
CURRENT_TIMESTAMP=""               # Current scan timestamp
CSV_FILE=""                        # Current CSV file path
TEMP_FILES=()                      # Array to track temporary files

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

# Error handling function
handle_error() {
    local error_code=$1
    local error_message="$2"
    local line_number=${3:-"unknown"}

    echo -e "${RED}❌ ERROR: $error_message (Line: $line_number)${NC}" >&2

    # Log error if verbose mode enabled
    if [[ "$VERBOSE" == "true" ]]; then
        echo "$(date -Iseconds) ERROR: $error_message (Line: $line_number)" >> "$LOG_FILE"
    fi

    # Cleanup temporary files and locks
    cleanup_temp_files
    remove_lock_file

    exit $error_code
}

# Trap for unexpected errors
trap 'handle_error $EXIT_DATA_ERROR "Unexpected error occurred" $LINENO' ERR

# Cleanup function for temporary files
cleanup_temp_files() {
    for temp_file in "${TEMP_FILES[@]}"; do
        [[ -f "$temp_file" ]] && rm -f "$temp_file"
    done
    TEMP_FILES=()
}

# Lock file management
create_lock_file() {
    if [[ -f "$LOCK_FILE" ]]; then
        local lock_pid
        lock_pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [[ -n "$lock_pid" ]] && kill -0 "$lock_pid" 2>/dev/null; then
            handle_error $EXIT_CONFIG_ERROR "Another instance is already running (PID: $lock_pid)"
        else
            echo "⚠️  Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
}

remove_lock_file() {
    [[ -f "$LOCK_FILE" ]] && rm -f "$LOCK_FILE"
}

# Path validation function
validate_path() {
    local path="$1"

    # Check for path traversal attempts
    if [[ "$path" =~ \.\./|\.\.\\ ]]; then
        handle_error $EXIT_CONFIG_ERROR "Invalid path detected: $path"
    fi

    # Ensure path is absolute
    if [[ "${path:0:1}" != "/" ]]; then
        handle_error $EXIT_CONFIG_ERROR "Path must be absolute: $path"
    fi

    # Check if path exists and is readable
    if [[ ! -r "$path" ]]; then
        handle_error $EXIT_PERMISSION_ERROR "Cannot read path: $path"
    fi
}

# Size formatting function
format_size() {
    local bytes=$1
    local units=("B" "KB" "MB" "GB" "TB")
    local size=$bytes
    local unit_index=0

    while (( size > 1024 && unit_index < 4 )); do
        size=$((size / 1024))
        ((unit_index++))
    done

    if (( unit_index == 0 )); then
        printf "%d %s" "$size" "${units[$unit_index]}"
    else
        printf "%.1f %s" "$(echo "scale=1; $bytes / (1024^$unit_index)" | bc -l)" "${units[$unit_index]}"
    fi
}

# Progress indicator for long-running operations
show_progress() {
    local current=$1
    local total=$2
    local operation=$3

    if [[ "$PROGRESS_INDICATORS" != "true" ]] || [[ "$SCRIPT_MODE" == "automated" ]]; then
        return
    fi

    local percent=$((current * 100 / total))
    local filled=$((percent / 2))
    local empty=$((50 - filled))

    printf "\r%s: [" "$operation"
    printf "%*s" $filled | tr ' ' '█'
    printf "%*s" $empty | tr ' ' '░'
    printf "] %d%% (%d/%d)" $percent $current $total
}

# Email content sanitization
sanitize_email_content() {
    local content="$1"

    # Remove potential email header injection characters
    content="${content//[$'\r\n']/}"
    content="${content//[<>]/}"

    echo "$content"
}

# Logging function
log_message() {
    local level="$1"
    local message="$2"

    if [[ "$VERBOSE" == "true" ]]; then
        echo "$(date -Iseconds) [$level] $message" >> "$LOG_FILE"
    fi

    if [[ "$SCRIPT_MODE" == "interactive" ]]; then
        case "$level" in
            "INFO")  echo -e "${BLUE}ℹ️  $message${NC}" ;;
            "WARN")  echo -e "${YELLOW}⚠️  $message${NC}" ;;
            "ERROR") echo -e "${RED}❌ $message${NC}" ;;
            "SUCCESS") echo -e "${GREEN}✅ $message${NC}" ;;
            *) echo "$message" ;;
        esac
    fi
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Get file size using appropriate stat command for the OS
get_file_size() {
    local filepath="$1"
    if [[ "$(uname)" == "Darwin" ]]; then
        stat -f "%z" "$filepath" 2>/dev/null
    else
        stat --format="%s" "$filepath" 2>/dev/null
    fi
}

# Get file modification time using appropriate stat command for the OS
get_file_mtime() {
    local filepath="$1"
    if [[ "$(uname)" == "Darwin" ]]; then
        stat -f "%m" "$filepath" 2>/dev/null
    else
        stat --format="%Y" "$filepath" 2>/dev/null
    fi
}

# Get available disk space for a file path
get_disk_free() {
    local filepath="$1"
    if [[ "$(uname)" == "Darwin" ]]; then
        df "$filepath" 2>/dev/null | tail -1 | awk '{print $4}'
    else
        df --output=avail "$filepath" 2>/dev/null | tail -1
    fi
}

# Get mount point for a file path
get_mount_point() {
    local filepath="$1"
    if [[ "$(uname)" == "Darwin" ]]; then
        df "$filepath" 2>/dev/null | tail -1 | awk '{print $9}'
    else
        df --output=target "$filepath" 2>/dev/null | tail -1
    fi
}

# Initialize data directory and files
initialize_data_directory() {
    # Ensure DATA_DIR is set with fallback
    if [[ -z "$DATA_DIR" ]] || [[ "$DATA_DIR" == "/.audit_disk" ]]; then
        # Fallback to /tmp if HOME is not set
        DATA_DIR="${HOME:-/tmp}/.audit_disk"
        STATE_FILE="$DATA_DIR/.state"
        LOG_FILE="$DATA_DIR/audit_disk.log"
        LOCK_FILE="$DATA_DIR/.lock"
    fi

    # Create data directory if it doesn't exist
    if [[ ! -d "$DATA_DIR" ]]; then
        mkdir -p "$DATA_DIR" || handle_error $EXIT_PERMISSION_ERROR "Cannot create data directory: $DATA_DIR"
        chmod 755 "$DATA_DIR"
    fi

    # Check write permissions
    if [[ ! -w "$DATA_DIR" ]]; then
        handle_error $EXIT_PERMISSION_ERROR "Data directory not writable: $DATA_DIR"
    fi

    # Initialize state file if it doesn't exist
    if [[ ! -f "$STATE_FILE" ]]; then
        touch "$STATE_FILE"
        chmod 600 "$STATE_FILE"
    fi

    # Set current timestamp and CSV file path
    CURRENT_TIMESTAMP=$(date -Iseconds)
    CSV_FILE="$DATA_DIR/audit_disk_$(date +%Y-%m-%d).csv"
}

# Validate configuration
validate_configuration() {
    # Check required commands
    local required_commands=("find" "df" "du" "awk" "sort" "date" "bc")
    for cmd in "${required_commands[@]}"; do
        if ! command_exists "$cmd"; then
            handle_error $EXIT_CONFIG_ERROR "Required command not found: $cmd"
        fi
    done

    # Check email command if in automated mode
    if [[ "$SCRIPT_MODE" == "automated" ]] && ! command_exists "mailx"; then
        handle_error $EXIT_CONFIG_ERROR "mailx command required for automated mode"
    fi

    # Validate numeric thresholds
    local numeric_vars=(
        "LARGE_FILE_THRESHOLD_MB" "GROWTH_PATTERN_THRESHOLD_MB"
        "DAILY_GROWTH_THRESHOLD_MB" "WEEKLY_GROWTH_THRESHOLD_MB"
        "DISK_SPACE_ALERT_GB" "DISK_SPACE_CHANGE_GB"
        "DATA_RETENTION_DAYS" "CONSECUTIVE_GROWTH_COUNT"
    )

    for var in "${numeric_vars[@]}"; do
        local value="${!var}"
        if ! [[ "$value" =~ ^[0-9]+$ ]]; then
            handle_error $EXIT_CONFIG_ERROR "Invalid numeric value for $var: $value"
        fi
    done

    # Validate email address format
    if [[ "$SCRIPT_MODE" == "automated" ]] && ! [[ "$EMAIL_RECIPIENT" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
        handle_error $EXIT_CONFIG_ERROR "Invalid email address: $EMAIL_RECIPIENT"
    fi
}

# =============================================================================
# DATA MANAGEMENT FUNCTIONS
# =============================================================================

# Get list of mounted filesystems to scan
get_mounted_filesystems() {
    local filesystems=()

    # Get all mounted filesystems
    while IFS= read -r line; do
        local mount_point filesystem_type
        mount_point=$(echo "$line" | awk '{print $6}')
        filesystem_type=$(echo "$line" | awk '{print $5}')

        # Skip excluded filesystems and mount types
        local skip=false
        for excluded in $EXCLUDED_FILESYSTEMS; do
            if [[ "$mount_point" == *"$excluded"* ]]; then
                skip=true
                break
            fi
        done

        for excluded_type in $EXCLUDED_MOUNT_TYPES; do
            if [[ "$filesystem_type" == "$excluded_type" ]]; then
                skip=true
                break
            fi
        done

        # Skip network filesystems if configured
        if [[ "$SKIP_NETWORK_FS" == "true" ]]; then
            case "$filesystem_type" in
                nfs|nfs4|cifs|smbfs|fuse.sshfs)
                    skip=true
                    ;;
            esac
        fi

        if [[ "$skip" == "false" ]] && [[ -d "$mount_point" ]] && [[ -r "$mount_point" ]]; then
            filesystems+=("$mount_point")
        fi
    done < <(df -T | tail -n +2)

    printf '%s\n' "${filesystems[@]}"
}

# Write CSV header if file doesn't exist
initialize_csv_file() {
    if [[ ! -f "$CSV_FILE" ]]; then
        echo "timestamp,filepath,size_bytes,disk_free_bytes,mount_point,scan_type" > "$CSV_FILE"
        chmod 600 "$CSV_FILE"
    fi
}

# Add entry to CSV file
add_csv_entry() {
    local filepath="$1"
    local size_bytes="$2"
    local disk_free_bytes="$3"
    local mount_point="$4"
    local scan_type="$5"

    echo "$CURRENT_TIMESTAMP,$filepath,$size_bytes,$disk_free_bytes,$mount_point,$scan_type" >> "$CSV_FILE"
}

# Get unique files from historical data
get_unique_files_from_history() {
    local temp_file
    temp_file=$(mktemp)
    TEMP_FILES+=("$temp_file")

    # Get all CSV files within retention period
    find "$DATA_DIR" -name "audit_disk_*.csv" -mtime -$DATA_RETENTION_DAYS -type f | while read -r csv_file; do
        if [[ -f "$csv_file" ]]; then
            tail -n +2 "$csv_file" | cut -d',' -f2 >> "$temp_file"
        fi
    done

    # Return unique file paths
    sort "$temp_file" | uniq
}

# Clean up old data files
cleanup_old_data() {
    log_message "INFO" "Cleaning up old data files"

    # Remove CSV files older than retention period
    find "$DATA_DIR" -name "audit_disk_*.csv" -mtime +$DATA_RETENTION_DAYS -type f -delete

    # Clean up old log files (keep last 3 rotations)
    if [[ -f "$LOG_FILE" ]] && [[ $(stat -c%s "$LOG_FILE" 2>/dev/null || echo 0) -gt 10485760 ]]; then
        # Rotate log file
        for i in {2..1}; do
            [[ -f "$LOG_FILE.$i" ]] && mv "$LOG_FILE.$i" "$LOG_FILE.$((i+1))"
        done
        [[ -f "$LOG_FILE" ]] && mv "$LOG_FILE" "$LOG_FILE.1"
        touch "$LOG_FILE"
        chmod 600 "$LOG_FILE"

        # Remove old rotations
        find "$DATA_DIR" -name "audit_disk.log.*" -type f | sort -V | head -n -3 | xargs -r rm -f
    fi
}

# Update state file
update_state() {
    local key="$1"
    local value="$2"

    # Create temporary file for atomic update
    local temp_state
    temp_state=$(mktemp)
    TEMP_FILES+=("$temp_state")

    # Copy existing state, excluding the key we're updating
    if [[ -f "$STATE_FILE" ]]; then
        grep -v "^$key=" "$STATE_FILE" > "$temp_state" 2>/dev/null || true
    fi

    # Add new key-value pair
    echo "$key=$value" >> "$temp_state"

    # Atomically replace state file
    mv "$temp_state" "$STATE_FILE"
    chmod 600 "$STATE_FILE"
}

# Get state value
get_state() {
    local key="$1"
    local default_value="$2"

    if [[ -f "$STATE_FILE" ]]; then
        grep "^$key=" "$STATE_FILE" | cut -d'=' -f2- | tail -1
    else
        echo "$default_value"
    fi
}

# =============================================================================
# SCANNING FUNCTIONS
# =============================================================================

# Scan single filesystem for large files
scan_single_filesystem() {
    local filesystem="$1"
    local scan_type="${2:-initial}"
    local file_count=0

    log_message "INFO" "Scanning filesystem: $filesystem"

    # Use timeout to prevent hanging on network filesystems
    local find_cmd="find '$filesystem' -type f -size +${LARGE_FILE_THRESHOLD_MB}M"

    # Add depth limit if configured
    if [[ "$MAX_FIND_DEPTH" -gt 0 ]]; then
        find_cmd="$find_cmd -maxdepth $MAX_FIND_DEPTH"
    fi

    # Add exclusions for common system directories
    find_cmd="$find_cmd -not -path '*/proc/*' -not -path '*/sys/*' -not -path '*/dev/*'"

    # Execute find with timeout
    # Use different stat format for macOS vs Linux
    local stat_format
    if [[ "$(uname)" == "Darwin" ]]; then
        stat_format="stat -f '%m %z %N'"
    else
        stat_format="stat --format='%Y %s %n'"
    fi

    eval "timeout $FIND_TIMEOUT_SECONDS $find_cmd -exec $stat_format {} \;" 2>/dev/null | \
    while IFS=' ' read -r timestamp size filepath; do
        # Skip if file path is empty or invalid
        [[ -z "$filepath" ]] && continue

        # Get disk free space and mount point for this file
        local disk_free mount_point
        disk_free=$(get_disk_free "$filepath")
        mount_point=$(get_mount_point "$filepath")

        # Skip if we couldn't get disk info
        [[ -z "$disk_free" ]] || [[ -z "$mount_point" ]] && continue

        # Convert to bytes
        disk_free=$((disk_free * 1024))

        # Add to CSV
        add_csv_entry "$filepath" "$size" "$disk_free" "$mount_point" "$scan_type"

        ((file_count++))

        # Show progress for interactive mode
        if [[ "$SCRIPT_MODE" == "interactive" ]] && (( file_count % 100 == 0 )); then
            echo -ne "\r🔍 Found $file_count large files in $filesystem..."
        fi
    done

    if [[ "$SCRIPT_MODE" == "interactive" ]]; then
        echo -e "\r🔍 Found $file_count large files in $filesystem"
    fi

    log_message "INFO" "Completed scanning $filesystem: $file_count files found"
}

# Perform initial scan of all filesystems
perform_initial_scan() {
    log_message "INFO" "Starting initial filesystem scan"

    local filesystems
    if [[ -n "$TARGET_DIR" ]]; then
        validate_path "$TARGET_DIR"
        filesystems=("$TARGET_DIR")
    else
        readarray -t filesystems < <(get_mounted_filesystems)
    fi

    if [[ ${#filesystems[@]} -eq 0 ]]; then
        log_message "WARN" "No filesystems found to scan"
        return
    fi

    log_message "INFO" "Scanning ${#filesystems[@]} filesystem(s)"

    if [[ "$PARALLEL_SCAN_ENABLED" == "true" ]] && [[ ${#filesystems[@]} -gt 1 ]]; then
        scan_filesystems_parallel "${filesystems[@]}"
    else
        for filesystem in "${filesystems[@]}"; do
            scan_single_filesystem "$filesystem" "initial"
        done
    fi

    log_message "SUCCESS" "Initial scan completed"
}

# Parallel filesystem scanning
scan_filesystems_parallel() {
    local filesystems=("$@")
    local -a pids=()
    local max_parallel=4

    for filesystem in "${filesystems[@]}"; do
        # Wait if we've reached max parallel processes
        while (( ${#pids[@]} >= max_parallel )); do
            for i in "${!pids[@]}"; do
                if ! kill -0 "${pids[$i]}" 2>/dev/null; then
                    unset "pids[$i]"
                fi
            done
            pids=("${pids[@]}")  # Reindex array
            sleep 1
        done

        # Start new scan process
        scan_single_filesystem "$filesystem" "initial" &
        pids+=($!)
    done

    # Wait for all processes to complete
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
}

# Rescan previously identified files
perform_rescan() {
    log_message "INFO" "Starting rescan of previously identified files"

    local file_count=0
    local rescanned_count=0

    # Get unique files from history
    while IFS= read -r filepath; do
        [[ -z "$filepath" ]] && continue
        ((file_count++))

        # Check if file still exists
        if [[ -f "$filepath" ]]; then
            # Get current file size and disk info
            local current_size disk_free mount_point
            current_size=$(get_file_size "$filepath")
            disk_free=$(get_disk_free "$filepath")
            mount_point=$(get_mount_point "$filepath")

            if [[ -n "$current_size" ]] && [[ -n "$disk_free" ]] && [[ -n "$mount_point" ]]; then
                # Convert disk free to bytes
                disk_free=$((disk_free * 1024))

                # Add to CSV
                add_csv_entry "$filepath" "$current_size" "$disk_free" "$mount_point" "rescan"
                ((rescanned_count++))
            fi
        fi

        # Show progress
        if [[ "$SCRIPT_MODE" == "interactive" ]] && (( file_count % 50 == 0 )); then
            echo -ne "\r🔄 Rescanned $rescanned_count/$file_count files..."
        fi
    done < <(get_unique_files_from_history)

    if [[ "$SCRIPT_MODE" == "interactive" ]]; then
        echo -e "\r🔄 Rescanned $rescanned_count/$file_count files"
    fi

    log_message "INFO" "Rescan completed: $rescanned_count/$file_count files still exist"
}

# Discover new large files since last scan
discover_new_files() {
    log_message "INFO" "Discovering new large files"

    # Get timestamp of last scan
    local last_scan_timestamp
    last_scan_timestamp=$(get_state "last_scan_timestamp" "1970-01-01T00:00:00")

    # Convert to epoch time for find
    local last_scan_epoch
    last_scan_epoch=$(date -d "$last_scan_timestamp" +%s 2>/dev/null || echo 0)

    local filesystems
    if [[ -n "$TARGET_DIR" ]]; then
        filesystems=("$TARGET_DIR")
    else
        readarray -t filesystems < <(get_mounted_filesystems)
    fi

    local new_files_count=0

    for filesystem in "${filesystems[@]}"; do
        # Find files modified since last scan
        # Use different stat format for macOS vs Linux
        local stat_format
        if [[ "$(uname)" == "Darwin" ]]; then
            stat_format="stat -f '%m %z %N'"
        else
            stat_format="stat --format='%Y %s %n'"
        fi

        find "$filesystem" -type f -size +${LARGE_FILE_THRESHOLD_MB}M -newermt "@$last_scan_epoch" \
             -not -path '*/proc/*' -not -path '*/sys/*' -not -path '*/dev/*' \
             -exec $stat_format {} \; 2>/dev/null | \
        while IFS=' ' read -r timestamp size filepath; do
            [[ -z "$filepath" ]] && continue

            # Check if this file is already in our current CSV
            if ! grep -q ",$filepath," "$CSV_FILE" 2>/dev/null; then
                local disk_free mount_point
                disk_free=$(get_disk_free "$filepath")
                mount_point=$(get_mount_point "$filepath")

                if [[ -n "$disk_free" ]] && [[ -n "$mount_point" ]]; then
                    disk_free=$((disk_free * 1024))
                    add_csv_entry "$filepath" "$size" "$disk_free" "$mount_point" "new"
                    ((new_files_count++))
                fi
            fi
        done
    done

    log_message "INFO" "Discovered $new_files_count new large files"
}

# =============================================================================
# ANALYSIS FUNCTIONS
# =============================================================================

# Analyze growth patterns and generate alerts
analyze_growth_patterns() {
    log_message "INFO" "Analyzing growth patterns"

    local temp_analysis
    temp_analysis=$(mktemp)
    TEMP_FILES+=("$temp_analysis")

    # Get all CSV files within analysis period
    find "$DATA_DIR" -name "audit_disk_*.csv" -mtime -$DATA_RETENTION_DAYS -type f | sort | while read -r csv_file; do
        [[ -f "$csv_file" ]] && tail -n +2 "$csv_file"
    done > "$temp_analysis"

    # Analyze each unique file for growth patterns
    local growth_pattern_alerts=()
    local daily_growth_alerts=()
    local weekly_growth_alerts=()

    while IFS= read -r filepath; do
        analyze_file_growth "$filepath" "$temp_analysis"
    done < <(cut -d',' -f2 "$temp_analysis" | sort | uniq)

    log_message "INFO" "Growth pattern analysis completed"
}

# Analyze growth for a specific file
analyze_file_growth() {
    local filepath="$1"
    local analysis_file="$2"

    # Get all entries for this file, sorted by timestamp
    local temp_file_data
    temp_file_data=$(mktemp)
    TEMP_FILES+=("$temp_file_data")

    grep ",$filepath," "$analysis_file" | sort -t',' -k1 > "$temp_file_data"

    local line_count
    line_count=$(wc -l < "$temp_file_data")

    # Need at least 2 data points for growth analysis
    [[ $line_count -lt 2 ]] && return

    # Get first and last entries
    local first_entry last_entry
    first_entry=$(head -1 "$temp_file_data")
    last_entry=$(tail -1 "$temp_file_data")

    # Parse entries
    local first_timestamp first_size last_timestamp last_size
    IFS=',' read -r first_timestamp _ first_size _ _ _ <<< "$first_entry"
    IFS=',' read -r last_timestamp _ last_size _ _ _ <<< "$last_entry"

    # Calculate growth
    local size_growth=$((last_size - first_size))
    local size_growth_mb=$((size_growth / 1048576))

    # Skip if no significant growth
    [[ $size_growth_mb -lt 1 ]] && return

    # Check for consecutive growth pattern
    check_consecutive_growth "$filepath" "$temp_file_data" "$size_growth_mb"

    # Check for 24-hour growth
    check_daily_growth "$filepath" "$temp_file_data" "$size_growth_mb"

    # Check for 7-day growth
    check_weekly_growth "$filepath" "$temp_file_data" "$size_growth_mb"
}

# Check for consecutive growth pattern
check_consecutive_growth() {
    local filepath="$1"
    local file_data="$2"
    local total_growth_mb="$3"

    # Count consecutive growth scans
    local consecutive_count=0
    local prev_size=0
    local current_consecutive=0

    while IFS=',' read -r timestamp _ size _ _ _; do
        if [[ $prev_size -gt 0 ]] && [[ $size -gt $prev_size ]]; then
            ((current_consecutive++))
        else
            if [[ $current_consecutive -gt $consecutive_count ]]; then
                consecutive_count=$current_consecutive
            fi
            current_consecutive=0
        fi
        prev_size=$size
    done < "$file_data"

    # Check final consecutive count
    if [[ $current_consecutive -gt $consecutive_count ]]; then
        consecutive_count=$current_consecutive
    fi

    # Update state with consecutive count
    update_state "consecutive_growth_$filepath" "$consecutive_count"

    # Check if meets alert criteria
    if [[ $consecutive_count -ge $CONSECUTIVE_GROWTH_COUNT ]] && [[ $total_growth_mb -ge $GROWTH_PATTERN_THRESHOLD_MB ]]; then
        echo "GROWTH_PATTERN:$filepath:$size:$total_growth_mb:$consecutive_count" >> "$DATA_DIR/.alerts"
    fi
}

# Check for 24-hour growth
check_daily_growth() {
    local filepath="$1"
    local file_data="$2"
    local total_growth_mb="$3"

    local current_time
    current_time=$(date +%s)
    local day_ago=$((current_time - 86400))

    # Find entry closest to 24 hours ago
    local closest_entry=""
    local closest_diff=999999999

    while IFS=',' read -r timestamp _ size _ _ _; do
        local entry_time
        entry_time=$(date -d "$timestamp" +%s 2>/dev/null || echo 0)
        local time_diff=$((entry_time - day_ago))
        time_diff=${time_diff#-}  # Absolute value

        if [[ $time_diff -lt $closest_diff ]]; then
            closest_diff=$time_diff
            closest_entry="$timestamp,$size"
        fi
    done < "$file_data"

    if [[ -n "$closest_entry" ]]; then
        local old_size current_size
        IFS=',' read -r _ old_size <<< "$closest_entry"
        current_size=$(tail -1 "$file_data" | cut -d',' -f3)

        local growth_24h=$((current_size - old_size))
        local growth_24h_mb=$((growth_24h / 1048576))

        if [[ $growth_24h_mb -ge $DAILY_GROWTH_THRESHOLD_MB ]]; then
            echo "DAILY_GROWTH:$filepath:$old_size:$current_size:$growth_24h_mb" >> "$DATA_DIR/.alerts"
        fi
    fi
}

# Check for 7-day growth
check_weekly_growth() {
    local filepath="$1"
    local file_data="$2"
    local total_growth_mb="$3"

    local current_time
    current_time=$(date +%s)
    local week_ago=$((current_time - 604800))

    # Find entry closest to 7 days ago
    local closest_entry=""
    local closest_diff=999999999

    while IFS=',' read -r timestamp _ size _ _ _; do
        local entry_time
        entry_time=$(date -d "$timestamp" +%s 2>/dev/null || echo 0)
        local time_diff=$((entry_time - week_ago))
        time_diff=${time_diff#-}  # Absolute value

        if [[ $time_diff -lt $closest_diff ]]; then
            closest_diff=$time_diff
            closest_entry="$timestamp,$size"
        fi
    done < "$file_data"

    if [[ -n "$closest_entry" ]]; then
        local old_size current_size
        IFS=',' read -r _ old_size <<< "$closest_entry"
        current_size=$(tail -1 "$file_data" | cut -d',' -f3)

        local growth_7d=$((current_size - old_size))
        local growth_7d_mb=$((growth_7d / 1048576))

        if [[ $growth_7d_mb -ge $WEEKLY_GROWTH_THRESHOLD_MB ]]; then
            echo "WEEKLY_GROWTH:$filepath:$old_size:$current_size:$growth_7d_mb" >> "$DATA_DIR/.alerts"
        fi
    fi
}

# Check disk space conditions
check_disk_space() {
    log_message "INFO" "Checking disk space conditions"

    # Get current disk space for all mount points
    while IFS= read -r line; do
        local filesystem size used avail use_percent mount_point
        read -r filesystem size used avail use_percent mount_point <<< "$line"

        # Skip if not a real filesystem
        [[ "$filesystem" == "Filesystem" ]] && continue
        [[ "$mount_point" =~ ^/(proc|sys|dev|run) ]] && continue

        # Convert to GB (avail is in KB)
        local avail_gb=$((avail / 1048576))

        # Check for low disk space
        if [[ $avail_gb -lt $DISK_SPACE_ALERT_GB ]]; then
            echo "LOW_DISK_SPACE:$mount_point:$avail_gb:$use_percent" >> "$DATA_DIR/.alerts"
        fi

        # Check for significant disk space decrease (compare with yesterday)
        local yesterday_free
        yesterday_free=$(get_state "disk_free_${mount_point//\//_}" "$avail")
        local decrease_gb=$(( (yesterday_free - avail) / 1048576 ))

        if [[ $decrease_gb -ge $DISK_SPACE_CHANGE_GB ]]; then
            echo "DISK_SPACE_DECREASE:$mount_point:$decrease_gb:$avail_gb" >> "$DATA_DIR/.alerts"
        fi

        # Update state with current free space
        update_state "disk_free_${mount_point//\//_}" "$avail"

    done < <(df -k)

    log_message "INFO" "Disk space analysis completed"
}

# =============================================================================
# ALERT FUNCTIONS
# =============================================================================

# Process all alerts and send notifications
process_alerts() {
    local alerts_file="$DATA_DIR/.alerts"

    # Initialize alerts file
    [[ -f "$alerts_file" ]] && rm -f "$alerts_file"
    touch "$alerts_file"

    # Run analysis to generate alerts
    analyze_growth_patterns
    check_disk_space

    # Check if any alerts were generated
    if [[ ! -s "$alerts_file" ]]; then
        log_message "INFO" "No alerts generated"
        return 0
    fi

    # Process alerts based on mode
    if [[ "$SCRIPT_MODE" == "automated" ]]; then
        send_email_alerts "$alerts_file"
    else
        display_interactive_alerts "$alerts_file"
    fi

    # Clean up alerts file
    rm -f "$alerts_file"
}

# Send email alerts
send_email_alerts() {
    local alerts_file="$1"

    # Check spam prevention for low disk space alerts
    if grep -q "LOW_DISK_SPACE" "$alerts_file"; then
        local last_email_time
        last_email_time=$(get_state "last_email_low_space" "1970-01-01T00:00:00")
        local last_email_epoch
        last_email_epoch=$(date -d "$last_email_time" +%s 2>/dev/null || echo 0)
        local current_epoch
        current_epoch=$(date +%s)
        local hours_since_last=$((( current_epoch - last_email_epoch ) / 3600))

        if [[ $hours_since_last -lt $EMAIL_SPAM_PREVENTION_HOURS ]]; then
            log_message "INFO" "Skipping low disk space email (spam prevention: ${hours_since_last}h < ${EMAIL_SPAM_PREVENTION_HOURS}h)"
            # Remove low disk space alerts but keep others
            grep -v "LOW_DISK_SPACE" "$alerts_file" > "${alerts_file}.tmp" && mv "${alerts_file}.tmp" "$alerts_file"

            # If no other alerts, return
            [[ ! -s "$alerts_file" ]] && return 0
        fi
    fi

    # Generate HTML email content
    local html_content
    html_content=$(generate_html_email "$alerts_file")

    # Determine alert type for subject
    local alert_type="Multiple Issues"
    if [[ $(wc -l < "$alerts_file") -eq 1 ]]; then
        local first_alert
        first_alert=$(head -1 "$alerts_file")
        case "${first_alert%%:*}" in
            "GROWTH_PATTERN") alert_type="Growth Pattern" ;;
            "DAILY_GROWTH") alert_type="24-Hour Growth" ;;
            "WEEKLY_GROWTH") alert_type="7-Day Growth" ;;
            "LOW_DISK_SPACE") alert_type="Low Disk Space" ;;
            "DISK_SPACE_DECREASE") alert_type="Disk Space Decrease" ;;
        esac
    fi

    # Send email
    local subject="$EMAIL_SUBJECT_PREFIX - $(hostname) - $alert_type"

    if echo "$html_content" | mailx -a "Content-Type: text/html" -s "$subject" "$EMAIL_RECIPIENT"; then
        log_message "SUCCESS" "Email alert sent successfully"

        # Update spam prevention timestamp for low disk space
        if grep -q "LOW_DISK_SPACE" "$alerts_file"; then
            update_state "last_email_low_space" "$CURRENT_TIMESTAMP"
        fi
    else
        log_message "ERROR" "Failed to send email alert"
        return $EXIT_EMAIL_ERROR
    fi
}

# Generate HTML email content
generate_html_email() {
    local alerts_file="$1"

    cat << EOF
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disk Audit Alert</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .alert-critical { background-color: #ffebee; border-left: 4px solid #f44336; padding: 15px; margin: 10px 0; }
        .alert-warning { background-color: #fff3e0; border-left: 4px solid #ff9800; padding: 15px; margin: 10px 0; }
        .alert-info { background-color: #e3f2fd; border-left: 4px solid #2196f3; padding: 15px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .size-large { font-weight: bold; color: #d32f2f; }
        .growth-high { background-color: #ffcdd2; }
        .growth-medium { background-color: #fff3e0; }
        h2 { color: #d32f2f; }
        h3 { color: #1976d2; }
        .summary { background-color: #f5f5f5; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <h2>🚨 Disk Audit Alert - $(hostname)</h2>
    <p><strong>Alert Time:</strong> $CURRENT_TIMESTAMP</p>
    <p><strong>Scan Type:</strong> Automated Monitoring</p>
EOF

    # Process each alert type
    local has_growth_pattern=false
    local has_daily_growth=false
    local has_weekly_growth=false
    local has_disk_space=false

    # Check what types of alerts we have
    while IFS=':' read -r alert_type rest; do
        case "$alert_type" in
            "GROWTH_PATTERN") has_growth_pattern=true ;;
            "DAILY_GROWTH") has_daily_growth=true ;;
            "WEEKLY_GROWTH") has_weekly_growth=true ;;
            "LOW_DISK_SPACE"|"DISK_SPACE_DECREASE") has_disk_space=true ;;
        esac
    done < "$alerts_file"

    # Generate growth pattern alerts table
    if [[ "$has_growth_pattern" == "true" ]]; then
        cat << EOF
    <div class="alert-critical">
        <h3>📈 Files with Sustained Growth Pattern</h3>
        <table>
            <tr>
                <th>File Path</th>
                <th>Current Size</th>
                <th>Growth (MB)</th>
                <th>Consecutive Scans</th>
            </tr>
EOF
        grep "^GROWTH_PATTERN:" "$alerts_file" | while IFS=':' read -r _ filepath size growth_mb consecutive; do
            local formatted_size
            formatted_size=$(format_size "$size")
            echo "            <tr class=\"growth-high\">"
            echo "                <td>$filepath</td>"
            echo "                <td class=\"size-large\">$formatted_size</td>"
            echo "                <td>+${growth_mb} MB</td>"
            echo "                <td>$consecutive</td>"
            echo "            </tr>"
        done
        echo "        </table>"
        echo "    </div>"
    fi

    # Generate 24-hour growth alerts table
    if [[ "$has_daily_growth" == "true" ]]; then
        cat << EOF
    <div class="alert-warning">
        <h3>⚡ 24-Hour Rapid Growth</h3>
        <table>
            <tr>
                <th>File Path</th>
                <th>Size 24h Ago</th>
                <th>Current Size</th>
                <th>Growth (MB)</th>
            </tr>
EOF
        grep "^DAILY_GROWTH:" "$alerts_file" | while IFS=':' read -r _ filepath old_size current_size growth_mb; do
            local formatted_old_size formatted_current_size
            formatted_old_size=$(format_size "$old_size")
            formatted_current_size=$(format_size "$current_size")
            echo "            <tr class=\"growth-medium\">"
            echo "                <td>$filepath</td>"
            echo "                <td>$formatted_old_size</td>"
            echo "                <td class=\"size-large\">$formatted_current_size</td>"
            echo "                <td>+${growth_mb} MB</td>"
            echo "            </tr>"
        done
        echo "        </table>"
        echo "    </div>"
    fi

    # Generate 7-day growth alerts table
    if [[ "$has_weekly_growth" == "true" ]]; then
        cat << EOF
    <div class="alert-info">
        <h3>📊 7-Day Sustained Growth</h3>
        <table>
            <tr>
                <th>File Path</th>
                <th>Size 7d Ago</th>
                <th>Current Size</th>
                <th>Growth (MB)</th>
            </tr>
EOF
        grep "^WEEKLY_GROWTH:" "$alerts_file" | while IFS=':' read -r _ filepath old_size current_size growth_mb; do
            local formatted_old_size formatted_current_size
            formatted_old_size=$(format_size "$old_size")
            formatted_current_size=$(format_size "$current_size")
            echo "            <tr>"
            echo "                <td>$filepath</td>"
            echo "                <td>$formatted_old_size</td>"
            echo "                <td class=\"size-large\">$formatted_current_size</td>"
            echo "                <td>+${growth_mb} MB</td>"
            echo "            </tr>"
        done
        echo "        </table>"
        echo "    </div>"
    fi

    # Generate disk space alerts table
    if [[ "$has_disk_space" == "true" ]]; then
        cat << EOF
    <div class="alert-critical">
        <h3>💾 Disk Space Issues</h3>
        <table>
            <tr>
                <th>Mount Point</th>
                <th>Issue Type</th>
                <th>Available Space</th>
                <th>Details</th>
            </tr>
EOF
        grep -E "^(LOW_DISK_SPACE|DISK_SPACE_DECREASE):" "$alerts_file" | while IFS=':' read -r alert_type mount_point value1 value2; do
            case "$alert_type" in
                "LOW_DISK_SPACE")
                    echo "            <tr class=\"growth-high\">"
                    echo "                <td>$mount_point</td>"
                    echo "                <td>Low Disk Space</td>"
                    echo "                <td class=\"size-large\">${value1} GB</td>"
                    echo "                <td>Usage: $value2</td>"
                    echo "            </tr>"
                    ;;
                "DISK_SPACE_DECREASE")
                    echo "            <tr class=\"growth-medium\">"
                    echo "                <td>$mount_point</td>"
                    echo "                <td>Space Decrease</td>"
                    echo "                <td>${value2} GB available</td>"
                    echo "                <td>Decreased by ${value1} GB</td>"
                    echo "            </tr>"
                    ;;
            esac
        done
        echo "        </table>"
        echo "    </div>"
    fi

    # Generate summary
    local total_alerts
    total_alerts=$(wc -l < "$alerts_file")

    cat << EOF
    <div class="summary">
        <h3>📊 Summary</h3>
        <ul>
            <li><strong>Total alerts:</strong> $total_alerts</li>
            <li><strong>Server:</strong> $(hostname)</li>
            <li><strong>Scan time:</strong> $CURRENT_TIMESTAMP</li>
            <li><strong>Next scan:</strong> $(date -d '+1 hour' -Iseconds)</li>
        </ul>

        <h4>Recommended Actions:</h4>
        <ul>
EOF

    if [[ "$has_growth_pattern" == "true" ]]; then
        echo "            <li>Review log rotation settings for files with sustained growth</li>"
    fi
    if [[ "$has_daily_growth" == "true" ]]; then
        echo "            <li>Investigate rapid file growth in the last 24 hours</li>"
    fi
    if [[ "$has_disk_space" == "true" ]]; then
        echo "            <li>Free up disk space or expand storage capacity</li>"
    fi

    cat << EOF
            <li>Monitor file growth trends and adjust thresholds if needed</li>
            <li>Consider implementing automated cleanup policies</li>
        </ul>
    </div>

    <hr>
    <p><em>This is an automated alert from the disk audit system on $(hostname).</em></p>
    <p><em>For questions or to modify alert settings, contact the system administration team.</em></p>
</body>
</html>
EOF
}

# Display alerts in interactive mode
display_interactive_alerts() {
    local alerts_file="$1"

    echo
    echo -e "${RED}🚨 ALERTS DETECTED${NC}"
    echo "═══════════════════════════════════════════════════════════════════════════════════"

    # Display growth pattern alerts
    if grep -q "^GROWTH_PATTERN:" "$alerts_file"; then
        echo
        echo -e "${RED}📈 Growth Pattern Alerts${NC}"
        echo "───────────────────────────────────────────────────────────────────────────────────"
        printf "%-50s %-12s %-12s %-8s\n" "File Path" "Current Size" "Growth (MB)" "Scans"
        echo "───────────────────────────────────────────────────────────────────────────────────"

        grep "^GROWTH_PATTERN:" "$alerts_file" | while IFS=':' read -r _ filepath size growth_mb consecutive; do
            local formatted_size
            formatted_size=$(format_size "$size")
            printf "%-50s %-12s %-12s %-8s\n" \
                "${filepath:0:49}" "$formatted_size" "+${growth_mb} MB" "$consecutive"
        done
    fi

    # Display 24-hour growth alerts
    if grep -q "^DAILY_GROWTH:" "$alerts_file"; then
        echo
        echo -e "${ORANGE}⚡ 24-Hour Growth Alerts${NC}"
        echo "───────────────────────────────────────────────────────────────────────────────────"
        printf "%-40s %-12s %-12s %-12s\n" "File Path" "Size 24h Ago" "Current Size" "Growth (MB)"
        echo "───────────────────────────────────────────────────────────────────────────────────"

        grep "^DAILY_GROWTH:" "$alerts_file" | while IFS=':' read -r _ filepath old_size current_size growth_mb; do
            local formatted_old_size formatted_current_size
            formatted_old_size=$(format_size "$old_size")
            formatted_current_size=$(format_size "$current_size")
            printf "%-40s %-12s %-12s %-12s\n" \
                "${filepath:0:39}" "$formatted_old_size" "$formatted_current_size" "+${growth_mb} MB"
        done
    fi

    # Display 7-day growth alerts
    if grep -q "^WEEKLY_GROWTH:" "$alerts_file"; then
        echo
        echo -e "${YELLOW}📊 7-Day Growth Alerts${NC}"
        echo "───────────────────────────────────────────────────────────────────────────────────"
        printf "%-40s %-12s %-12s %-12s\n" "File Path" "Size 7d Ago" "Current Size" "Growth (MB)"
        echo "───────────────────────────────────────────────────────────────────────────────────"

        grep "^WEEKLY_GROWTH:" "$alerts_file" | while IFS=':' read -r _ filepath old_size current_size growth_mb; do
            local formatted_old_size formatted_current_size
            formatted_old_size=$(format_size "$old_size")
            formatted_current_size=$(format_size "$current_size")
            printf "%-40s %-12s %-12s %-12s\n" \
                "${filepath:0:39}" "$formatted_old_size" "$formatted_current_size" "+${growth_mb} MB"
        done
    fi

    # Display disk space alerts
    if grep -qE "^(LOW_DISK_SPACE|DISK_SPACE_DECREASE):" "$alerts_file"; then
        echo
        echo -e "${RED}💾 Disk Space Alerts${NC}"
        echo "───────────────────────────────────────────────────────────────────────────────────"
        printf "%-20s %-20s %-15s %-20s\n" "Mount Point" "Issue Type" "Available" "Details"
        echo "───────────────────────────────────────────────────────────────────────────────────"

        grep -E "^(LOW_DISK_SPACE|DISK_SPACE_DECREASE):" "$alerts_file" | while IFS=':' read -r alert_type mount_point value1 value2; do
            case "$alert_type" in
                "LOW_DISK_SPACE")
                    printf "%-20s %-20s %-15s %-20s\n" \
                        "$mount_point" "Low Disk Space" "${value1} GB" "Usage: $value2"
                    ;;
                "DISK_SPACE_DECREASE")
                    printf "%-20s %-20s %-15s %-20s\n" \
                        "$mount_point" "Space Decrease" "${value2} GB" "Decreased ${value1} GB"
                    ;;
            esac
        done
    fi

    echo
    echo "═══════════════════════════════════════════════════════════════════════════════════"
}

# =============================================================================
# OUTPUT FORMATTING FUNCTIONS
# =============================================================================

# Display interactive mode header
display_header() {
    if [[ "$SCRIPT_MODE" != "interactive" ]]; then
        return
    fi

    echo
    echo -e "${BLUE}🔍 Disk Audit System - Interactive Mode${NC}"
    echo "═══════════════════════════════════════════════════════════════════════════════════"
    echo
    echo -e "${CYAN}📊 System Information${NC}"
    echo "├─ Hostname: $(hostname)"
    echo "├─ Scan Time: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "├─ Large File Threshold: ${LARGE_FILE_THRESHOLD_MB} MB"
    echo "├─ Data Directory: $DATA_DIR"
    if [[ -n "$TARGET_DIR" ]]; then
        echo "├─ Target Directory: $TARGET_DIR"
    fi
    echo "└─ Mode: $SCRIPT_MODE"
    echo
}

# Display scan results summary
display_scan_summary() {
    if [[ "$SCRIPT_MODE" != "interactive" ]]; then
        return
    fi

    local total_files
    total_files=$(wc -l < "$CSV_FILE" 2>/dev/null || echo 0)
    total_files=$((total_files - 1))  # Subtract header line

    local total_size=0
    if [[ -f "$CSV_FILE" ]] && [[ $total_files -gt 0 ]]; then
        total_size=$(tail -n +2 "$CSV_FILE" | cut -d',' -f3 | awk '{sum+=$1} END {print sum+0}')
    fi

    echo
    echo -e "${CYAN}📊 Scan Summary${NC}"
    echo "├─ Total files tracked: $(printf "%'d" $total_files)"
    echo "├─ Total size tracked: $(format_size $total_size)"
    echo "├─ CSV file: $(basename "$CSV_FILE")"
    echo "└─ Scan completed: $(date '+%Y-%m-%d %H:%M:%S')"
}

# Display large files table
display_large_files() {
    if [[ "$SCRIPT_MODE" != "interactive" ]] || [[ ! -f "$CSV_FILE" ]]; then
        return
    fi

    local file_count
    file_count=$(tail -n +2 "$CSV_FILE" | wc -l)

    if [[ $file_count -eq 0 ]]; then
        echo
        echo -e "${GREEN}✅ No large files found above ${LARGE_FILE_THRESHOLD_MB} MB threshold${NC}"
        return
    fi

    echo
    echo -e "${BLUE}📊 Large Files Found (>${LARGE_FILE_THRESHOLD_MB} MB)${NC}"
    echo "═══════════════════════════════════════════════════════════════════════════════════"
    printf "%-50s %-12s %-15s %-20s\n" "File Path" "Size" "Mount Point" "Last Modified"
    echo "───────────────────────────────────────────────────────────────────────────────────"

    # Sort by size (descending) and display top files
    tail -n +2 "$CSV_FILE" | sort -t',' -k3 -nr | head -20 | while IFS=',' read -r timestamp filepath size_bytes disk_free mount_point scan_type; do
        local formatted_size last_modified
        formatted_size=$(format_size "$size_bytes")
        last_modified=$(stat -c %y "$filepath" 2>/dev/null | cut -d'.' -f1 || echo "Unknown")

        printf "%-50s %-12s %-15s %-20s\n" \
            "${filepath:0:49}" "$formatted_size" "${mount_point:0:14}" "${last_modified:0:19}"
    done

    if [[ $file_count -gt 20 ]]; then
        echo "... and $((file_count - 20)) more files"
    fi

    echo "───────────────────────────────────────────────────────────────────────────────────"
}

# Display configuration
display_configuration() {
    echo
    echo -e "${BLUE}⚙️  Current Configuration${NC}"
    echo "═══════════════════════════════════════════════════════════════════════════════════"
    echo
    echo -e "${CYAN}File Size Thresholds:${NC}"
    echo "├─ Large File Threshold: ${LARGE_FILE_THRESHOLD_MB} MB"
    echo "├─ Growth Pattern Threshold: ${GROWTH_PATTERN_THRESHOLD_MB} MB"
    echo "├─ Daily Growth Threshold: ${DAILY_GROWTH_THRESHOLD_MB} MB"
    echo "└─ Weekly Growth Threshold: ${WEEKLY_GROWTH_THRESHOLD_MB} MB"
    echo
    echo -e "${CYAN}Disk Space Thresholds:${NC}"
    echo "├─ Low Disk Space Alert: ${DISK_SPACE_ALERT_GB} GB"
    echo "└─ Disk Space Change Alert: ${DISK_SPACE_CHANGE_GB} GB"
    echo
    echo -e "${CYAN}Data Management:${NC}"
    echo "├─ Data Retention: ${DATA_RETENTION_DAYS} days"
    echo "├─ Consecutive Growth Count: ${CONSECUTIVE_GROWTH_COUNT}"
    echo "├─ Data Directory: $DATA_DIR"
    echo "└─ State File: $STATE_FILE"
    echo
    echo -e "${CYAN}Email Configuration:${NC}"
    echo "├─ Recipient: $EMAIL_RECIPIENT"
    echo "├─ Subject Prefix: $EMAIL_SUBJECT_PREFIX"
    echo "└─ Spam Prevention: ${EMAIL_SPAM_PREVENTION_HOURS} hours"
    echo
    echo -e "${CYAN}Performance Settings:${NC}"
    echo "├─ Max Find Depth: $MAX_FIND_DEPTH"
    echo "├─ Find Timeout: ${FIND_TIMEOUT_SECONDS}s"
    echo "├─ Parallel Scanning: $PARALLEL_SCAN_ENABLED"
    echo "└─ Skip Network FS: $SKIP_NETWORK_FS"
    echo
}

# Test email functionality
test_email() {
    echo -e "${BLUE}📧 Testing Email Functionality${NC}"
    echo "═══════════════════════════════════════════════════════════════════════════════════"

    # Check if mailx is available
    if ! command_exists "mailx"; then
        echo -e "${RED}❌ mailx command not found${NC}"
        echo "Please install mailx package:"
        echo "  CentOS/RHEL: yum install mailx"
        echo "  Ubuntu/Debian: apt-get install mailutils"
        return $EXIT_CONFIG_ERROR
    fi

    # Generate test email
    local test_subject="$EMAIL_SUBJECT_PREFIX - $(hostname) - Test Email"
    local test_content="This is a test email from the disk audit system on $(hostname).

Test Details:
- Timestamp: $CURRENT_TIMESTAMP
- Script Version: 1.0
- Configuration: OK
- Email Recipient: $EMAIL_RECIPIENT

If you received this email, the email functionality is working correctly."

    echo "Sending test email to: $EMAIL_RECIPIENT"
    echo "Subject: $test_subject"
    echo

    if echo "$test_content" | mailx -s "$test_subject" "$EMAIL_RECIPIENT"; then
        echo -e "${GREEN}✅ Test email sent successfully${NC}"
        echo "Please check the recipient's inbox to confirm delivery."
        return 0
    else
        echo -e "${RED}❌ Failed to send test email${NC}"
        echo "Please check:"
        echo "  - SMTP configuration"
        echo "  - Email recipient address"
        echo "  - Network connectivity"
        echo "  - Mail server settings"
        return $EXIT_EMAIL_ERROR
    fi
}

# Display help information
display_help() {
    echo
    echo -e "${BLUE}🔍 Disk Audit System - Help${NC}"
    echo "═══════════════════════════════════════════════════════════════════════════════════"
    echo
    echo -e "${CYAN}USAGE:${NC}"
    echo "  $0 [OPTIONS]"
    echo
    echo -e "${CYAN}OPTIONS:${NC}"
    echo "  (no options)     Interactive mode - full filesystem scan with colored output"
    echo "  -audit           Automated mode - designed for cron, sends email alerts"
    echo "  -dir <path>      Directory-specific monitoring of specified path"
    echo "  -v, --verbose    Enable verbose logging to log file"
    echo "  -h, --help       Display this help message"
    echo "  --config         Display current configuration settings"
    echo "  --test-email     Test email functionality"
    echo "  --cleanup        Perform maintenance cleanup of old data"
    echo
    echo -e "${CYAN}EXAMPLES:${NC}"
    echo "  $0                           # Interactive scan of all filesystems"
    echo "  $0 -audit                    # Automated scan with email alerts"
    echo "  $0 -dir /var/log             # Monitor only /var/log directory"
    echo "  $0 -v                        # Interactive mode with verbose logging"
    echo "  $0 --test-email              # Test email configuration"
    echo
    echo -e "${CYAN}CRON SETUP:${NC}"
    echo "  # Hourly automated monitoring"
    echo "  0 * * * * $0 -audit"
    echo
    echo -e "${CYAN}CONFIGURATION:${NC}"
    echo "  Edit the configuration variables at the top of this script to customize:"
    echo "  - File size thresholds"
    echo "  - Email recipient and settings"
    echo "  - Data retention period"
    echo "  - Alert thresholds"
    echo
    echo -e "${CYAN}DATA LOCATION:${NC}"
    echo "  - Data Directory: $DATA_DIR"
    echo "  - CSV Files: audit_disk_YYYY-MM-DD.csv"
    echo "  - State File: .state"
    echo "  - Log File: audit_disk.log (if verbose mode enabled)"
    echo
}

# =============================================================================
# MAIN EXECUTION LOGIC
# =============================================================================

# Parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -audit)
                SCRIPT_MODE="automated"
                shift
                ;;
            -dir)
                if [[ -z "$2" ]]; then
                    handle_error $EXIT_CONFIG_ERROR "Directory path required for -dir option"
                fi
                TARGET_DIR="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                display_help
                exit $EXIT_SUCCESS
                ;;
            --config)
                display_configuration
                exit $EXIT_SUCCESS
                ;;
            --test-email)
                initialize_data_directory
                test_email
                exit $?
                ;;
            --cleanup)
                initialize_data_directory
                cleanup_old_data
                echo -e "${GREEN}✅ Cleanup completed${NC}"
                exit $EXIT_SUCCESS
                ;;
            *)
                handle_error $EXIT_CONFIG_ERROR "Unknown option: $1"
                ;;
        esac
    done
}

# Main workflow
main() {
    local start_time
    start_time=$(date +%s)

    # Parse command line arguments
    parse_arguments "$@"

    # Initialize system
    initialize_data_directory
    validate_configuration
    create_lock_file

    # Display header for interactive mode
    display_header

    # Initialize CSV file
    initialize_csv_file

    # Perform scanning
    log_message "INFO" "Starting disk audit scan"

    # Check if this is first run or rescan
    local last_scan_timestamp
    last_scan_timestamp=$(get_state "last_scan_timestamp" "")

    if [[ -z "$last_scan_timestamp" ]]; then
        log_message "INFO" "Performing initial scan"
        perform_initial_scan
    else
        log_message "INFO" "Performing rescan and new file discovery"
        perform_rescan
        discover_new_files
    fi

    # Update last scan timestamp
    update_state "last_scan_timestamp" "$CURRENT_TIMESTAMP"

    # Display results for interactive mode
    display_large_files
    display_scan_summary

    # Process alerts
    process_alerts

    # Cleanup old data
    cleanup_old_data

    # Calculate execution time
    local end_time duration
    end_time=$(date +%s)
    duration=$((end_time - start_time))

    log_message "SUCCESS" "Disk audit completed in ${duration}s"

    if [[ "$SCRIPT_MODE" == "interactive" ]]; then
        echo
        echo -e "${GREEN}✅ Scan completed successfully in ${duration}s${NC}"
        echo
    fi

    # Cleanup and exit
    cleanup_temp_files
    remove_lock_file

    exit $EXIT_SUCCESS
}

# Health check function
health_check() {
    local issues=0

    echo -e "${BLUE}🏥 System Health Check${NC}"
    echo "═══════════════════════════════════════════════════════════════════════════════════"

    # Check data directory accessibility
    if [[ ! -w "$DATA_DIR" ]]; then
        echo -e "${RED}❌ Data directory not writable: $DATA_DIR${NC}"
        ((issues++))
    else
        echo -e "${GREEN}✅ Data directory accessible${NC}"
    fi

    # Check email functionality
    if ! command_exists "mailx"; then
        echo -e "${RED}❌ mailx command not available${NC}"
        ((issues++))
    else
        echo -e "${GREEN}✅ mailx command available${NC}"
    fi

    # Check disk space for data directory
    local data_dir_free
    data_dir_free=$(df --output=avail "$DATA_DIR" | tail -1)
    if (( data_dir_free < 1048576 )); then  # Less than 1GB
        echo -e "${YELLOW}⚠️  Low disk space in data directory: $(format_size $((data_dir_free * 1024)))${NC}"
        ((issues++))
    else
        echo -e "${GREEN}✅ Sufficient disk space in data directory${NC}"
    fi

    # Check for stale lock files
    if [[ -f "$LOCK_FILE" ]]; then
        local lock_age
        lock_age=$(( $(date +%s) - $(stat -c %Y "$LOCK_FILE") ))
        if (( lock_age > 7200 )); then  # Older than 2 hours
            echo -e "${YELLOW}⚠️  Stale lock file detected (age: ${lock_age}s)${NC}"
            ((issues++))
        else
            echo -e "${GREEN}✅ Lock file status normal${NC}"
        fi
    else
        echo -e "${GREEN}✅ No lock file present${NC}"
    fi

    # Check required commands
    local required_commands=("find" "df" "du" "awk" "sort" "date" "bc")
    local missing_commands=0
    for cmd in "${required_commands[@]}"; do
        if ! command_exists "$cmd"; then
            echo -e "${RED}❌ Required command missing: $cmd${NC}"
            ((missing_commands++))
        fi
    done

    if [[ $missing_commands -eq 0 ]]; then
        echo -e "${GREEN}✅ All required commands available${NC}"
    else
        issues=$((issues + missing_commands))
    fi

    echo
    if (( issues == 0 )); then
        echo -e "${GREEN}✅ Health check passed - system is ready${NC}"
        return 0
    else
        echo -e "${RED}❌ Health check found $issues issues${NC}"
        return 1
    fi
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Ensure script is not sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    echo "Error: This script should be executed, not sourced" >&2
    return 1
fi

# Check if running as root (warn but don't prevent)
if [[ $EUID -eq 0 ]]; then
    echo -e "${YELLOW}⚠️  Warning: Running as root. Consider using a dedicated user account.${NC}" >&2
fi

# Main execution
main "$@"