#!/bin/bash
#
# audit_disk.sh - Comprehensive disk usage monitoring with alerting
# Version: 1.0
# Author: System Administration Team
# Date: 2025-01-14
#
# Description:
# This script monitors disk usage and file growth patterns across multiple
# filesystems, tracks large files, and sends email alerts when thresholds
# are exceeded.

# Exit on error, undefined variables, and pipe failures
set -euo pipefail

#=============================================================================
# CONFIGURATION SECTION
#=============================================================================

# File Size Thresholds (in MB)
LARGE_FILE_THRESHOLD_MB=250          # Initial scan minimum file size
GROWTH_PATTERN_THRESHOLD_MB=150      # Cumulative growth for pattern alerts
DAILY_GROWTH_THRESHOLD_MB=300        # 24-hour growth threshold
WEEKLY_GROWTH_THRESHOLD_MB=500       # 7-day growth threshold

# Disk Space Thresholds (in GB)
DISK_SPACE_ALERT_GB=10              # Low disk space alert threshold
DISK_SPACE_CHANGE_GB=20             # Disk space decrease alert threshold

# Data Management
DATA_RETENTION_DAYS=7               # CSV file retention period
CONSECUTIVE_GROWTH_COUNT=3          # Required consecutive growth scans

# Email Configuration
EMAIL_RECIPIENT="<EMAIL>"
EMAIL_SUBJECT_PREFIX="Disk Audit Alert"
EMAIL_SPAM_PREVENTION_HOURS=6       # Minimum hours between low space alerts

# System Paths
DATA_DIR="/var/lib/audit_disk"      # Data storage directory
STATE_FILE="$DATA_DIR/.state"       # State tracking file
LOG_FILE="$DATA_DIR/audit_disk.log" # Optional verbose logging

# Filesystem Exclusions
EXCLUDED_FILESYSTEMS="proc sys dev tmp run"
EXCLUDED_MOUNT_TYPES="tmpfs devtmpfs sysfs proc"

# Advanced Configuration Options
MAX_FIND_DEPTH=10                   # Maximum directory traversal depth
FIND_TIMEOUT_SECONDS=3600           # Timeout for find operations
PARALLEL_SCAN_ENABLED=true          # Enable parallel filesystem scanning

# Network Filesystem Handling
NETWORK_FS_TIMEOUT=30               # Timeout for network filesystem operations
SKIP_NETWORK_FS=true                # Skip NFS, CIFS, etc.

# Output Formatting
COLOR_OUTPUT_ENABLED=true           # Enable color output in interactive mode
PROGRESS_INDICATORS=true            # Show progress during long operations
TABLE_WIDTH=120                     # Maximum table width for formatting

# Runtime variables
INTERACTIVE_MODE=true               # Default to interactive mode
VERBOSE_MODE=false                  # Default to non-verbose mode
TARGET_DIR=""                       # Default to scan all filesystems
CURRENT_DATE=$(date +%Y-%m-%d)      # Current date for file naming
CSV_FILE="$DATA_DIR/audit_disk_$CURRENT_DATE.csv"  # Today's CSV file

#=============================================================================
# UTILITY FUNCTIONS
#=============================================================================

# Initialize script environment
init_environment() {
    # Create data directory if it doesn't exist
    if [[ ! -d "$DATA_DIR" ]]; then
        mkdir -p "$DATA_DIR" 2>/dev/null || {
            echo "❌ Error: Cannot create data directory: $DATA_DIR"
            exit 1
        }
    fi
    
    # Initialize state file if it doesn't exist
    if [[ ! -f "$STATE_FILE" ]]; then
        echo "# Audit Disk State File - Created $(date -Iseconds)" > "$STATE_FILE"
        echo "last_scan_timestamp=$(date -Iseconds)" >> "$STATE_FILE"
        echo "total_files_tracked=0" >> "$STATE_FILE"
    fi
    
    # Initialize CSV file if it doesn't exist
    if [[ ! -f "$CSV_FILE" ]]; then
        echo "timestamp,filepath,size_bytes,disk_free_bytes,mount_point,scan_type" > "$CSV_FILE"
    fi
    
    # Set up logging
    if [[ "$VERBOSE_MODE" == true ]]; then
        exec 3>&1 4>&2
        trap 'exec 2>&4 1>&3' EXIT
        exec 1>>"$LOG_FILE" 2>&1
        echo "==============================================="
        echo "Log started at $(date -Iseconds)"
        echo "==============================================="
    fi
}

# Log message with timestamp
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date -Iseconds)
    
    if [[ "$VERBOSE_MODE" == true ]]; then
        echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
    fi
    
    # Only show info messages in interactive mode or if verbose
    if [[ "$level" != "INFO" ]] || [[ "$INTERACTIVE_MODE" == true ]] || [[ "$VERBOSE_MODE" == true ]]; then
        case "$level" in
            "INFO")
                prefix="🔍"
                ;;
            "WARNING")
                prefix="⚠️"
                ;;
            "ERROR")
                prefix="❌"
                ;;
            "SUCCESS")
                prefix="✅"
                ;;
            *)
                prefix="📌"
                ;;
        esac
        
        echo "$prefix $message"
    fi
}

# Format file size to human-readable format
format_size() {
    local size="$1"
    local units=("B" "KB" "MB" "GB" "TB")
    local unit=0
    
    while (( size > 1024 && unit < 4 )); do
        size=$(( size / 1024 ))
        (( unit++ ))
    done
    
    echo "$size ${units[$unit]}"
}

# Validate path to prevent command injection
validate_path() {
    local path="$1"
    
    # Remove any characters that could be used for command injection
    local sanitized_path=$(echo "$path" | tr -cd '[:alnum:]._/-')
    
    if [[ "$path" != "$sanitized_path" ]]; then
        log_message "ERROR" "Invalid path detected: $path"
        return 1
    fi
    
    return 0
}

# Check if filesystem should be excluded
is_excluded_filesystem() {
    local mount_point="$1"
    
    # Check against excluded filesystems
    for excluded in $EXCLUDED_FILESYSTEMS; do
        if [[ "$mount_point" == "/$excluded" || "$mount_point" == "/$excluded/"* ]]; then
            return 0
        fi
    done
    
    # Check against excluded mount types
    local mount_type=$(df -T "$mount_point" | awk 'NR==2 {print $2}')
    for excluded in $EXCLUDED_MOUNT_TYPES; do
        if [[ "$mount_type" == "$excluded" ]]; then
            return 0
        fi
    done
    
    # Check if it's a network filesystem and we're skipping those
    if [[ "$SKIP_NETWORK_FS" == true ]]; then
        if [[ "$mount_type" == "nfs" || "$mount_type" == "cifs" || "$mount_type" == "smbfs" ]]; then
            return 0
        fi
    fi
    
    return 1
}

# Get value from state file
get_state_value() {
    local key="$1"
    local default_value="${2:-}"
    
    if [[ -f "$STATE_FILE" ]]; then
        local value=$(grep "^$key=" "$STATE_FILE" | cut -d= -f2)
        if [[ -n "$value" ]]; then
            echo "$value"
            return 0
        fi
    fi
    
    echo "$default_value"
    return 1
}

# Set value in state file
set_state_value() {
    local key="$1"
    local value="$2"
    
    if [[ -f "$STATE_FILE" ]]; then
        # If key exists, update it
        if grep -q "^$key=" "$STATE_FILE"; then
            sed -i "s|^$key=.*|$key=$value|" "$STATE_FILE"
        else
            # Otherwise append it
            echo "$key=$value" >> "$STATE_FILE"
        fi
    else
        # Create state file if it doesn't exist
        echo "$key=$value" > "$STATE_FILE"
    fi
}

#=============================================================================
# DATA MANAGEMENT FUNCTIONS
#=============================================================================

# Clean up old CSV files
cleanup_old_data() {
    log_message "INFO" "Cleaning up old data files..."
    
    # Find and delete CSV files older than DATA_RETENTION_DAYS
    find "$DATA_DIR" -name "audit_disk_*.csv" -type f -mtime +$DATA_RETENTION_DAYS -delete
    
    # Clean up state file by removing obsolete entries
    optimize_state_file
    
    log_message "SUCCESS" "Data cleanup completed"
}

# Optimize state file by removing obsolete entries
optimize_state_file() {
    local temp_file="$STATE_FILE.tmp"
    
    # Keep essential entries
    grep "^last_scan_timestamp=" "$STATE_FILE" > "$temp_file"
    grep "^total_files_tracked=" "$STATE_FILE" >> "$temp_file"
    
    # Keep email timestamp entries
    grep "^last_email_" "$STATE_FILE" >> "$temp_file"
    
    # Keep consecutive growth entries for files that still exist
    grep "^consecutive_growth_" "$STATE_FILE" | while read line; do
        local file_path=$(echo "$line" | cut -d= -f1 | sed 's/^consecutive_growth_//')
        if [[ -f "$file_path" ]]; then
            echo "$line" >> "$temp_file"
        fi
    done
    
    # Replace original with optimized version
    mv "$temp_file" "$STATE_FILE"
}

# Verify data integrity
verify_data_integrity() {
    log_message "INFO" "Verifying data integrity..."
    
    # Check CSV file format
    if [[ -f "$CSV_FILE" ]]; then
        local header=$(head -n 1 "$CSV_FILE")
        local expected_header="timestamp,filepath,size_bytes,disk_free_bytes,mount_point,scan_type"
        
        if [[ "$header" != "$expected_header" ]]; then
            log_message "WARNING" "CSV file has incorrect header format"
            echo "$expected_header" > "$CSV_FILE.fixed"
            tail -n +2 "$CSV_FILE" >> "$CSV_FILE.fixed"
            mv "$CSV_FILE.fixed" "$CSV_FILE"
        fi
    fi
    
    # Check state file format
    if [[ -f "$STATE_FILE" ]]; then
        if ! grep -q "^last_scan_timestamp=" "$STATE_FILE"; then
            log_message "WARNING" "State file missing required entries"
            echo "last_scan_timestamp=$(date -Iseconds)" >> "$STATE_FILE"
        fi
    fi
    
    log_message "SUCCESS" "Data integrity verified"
}

# Save file data to CSV
save_file_data() {
    local filepath="$1"
    local size_bytes="$2"
    local mount_point="$3"
    local scan_type="$4"
    
    # Validate inputs
    if ! validate_path "$filepath"; then
        return 1
    fi
    
    # Get disk free space
    local disk_free_bytes=$(df --output=avail "$mount_point" | tail -1)
    disk_free_bytes=$((disk_free_bytes * 1024))  # Convert from KB to bytes
    
    # Get current timestamp
    local timestamp=$(date -Iseconds)
    
    # Append to CSV file
    echo "$timestamp,$filepath,$size_bytes,$disk_free_bytes,$mount_point,$scan_type" >> "$CSV_FILE"
    
    return 0
}

#=============================================================================
# SCANNING FUNCTIONS
#=============================================================================

# Get list of mounted filesystems
get_mounted_filesystems() {
    if [[ -n "$TARGET_DIR" ]]; then
        # If target directory is specified, only return that
        echo "$TARGET_DIR"
    else
        # Otherwise get all mounted filesystems
        df --output=target | tail -n +2 | sort -u
    fi
}

# Scan filesystem for large files
scan_filesystem() {
    local mount_point="$1"
    local scan_count=0
    
    # Skip excluded filesystems
    if is_excluded_filesystem "$mount_point"; then
        log_message "INFO" "Skipping excluded filesystem: $mount_point"
        return 0
    fi
    
    log_message "INFO" "Scanning filesystem: $mount_point"
    
    # Use find to locate large files
    find "$mount_point" -xdev -type f -size "+${LARGE_FILE_THRESHOLD_MB}M" \
         -not -path "*/proc/*" -not -path "*/sys/*" -not -path "*/dev/*" \
         -not -path "*/run/*" -not -path "*/tmp/*" \
         -maxdepth $MAX_FIND_DEPTH 2>/dev/null | while read filepath; do
        
        # Get file size in bytes
        local size_bytes=$(stat -c %s "$filepath" 2>/dev/null)
        
        if [[ -n "$size_bytes" ]]; then
            # Save file data to CSV
            save_file_data "$filepath" "$size_bytes" "$mount_point" "scan"
            ((scan_count++))
            
            # Show progress in interactive mode
            if [[ "$INTERACTIVE_MODE" == true && "$PROGRESS_INDICATORS" == true ]]; then
                if ((scan_count % 10 == 0)); then
                    echo -n "."
                fi
            fi
        fi
    done
    
    if [[ "$INTERACTIVE_MODE" == true && "$PROGRESS_INDICATORS" == true ]]; then
        echo ""  # New line after progress dots
    fi
    
    log_message "SUCCESS" "Found $scan_count large files in $mount_point"
    
    return 0
}

# Perform initial scan of all filesystems
perform_initial_scan() {
    log_message "INFO" "Starting initial scan of filesystems"
    
    local total_count=0
    local filesystems=$(get_mounted_filesystems)
    
    for fs in $filesystems; do
        scan_filesystem "$fs"
        local count=$(grep ",$fs," "$CSV_FILE" | wc -l)
        ((total_count += count))
    done
    
    # Update state file
    set_state_value "last_scan_timestamp" "$(date -Iseconds)"
    set_state_value "total_files_tracked" "$total_count"
    
    log_message "SUCCESS" "Initial scan completed. Found $total_count large files."
    
    return 0
}

# Rescan files from previous scans to check for growth
perform_rescan() {
    log_message "INFO" "Starting rescan of previously tracked files"
    
    local rescan_count=0
    local growth_count=0
    
    # Get list of unique files from previous scans
    local previous_files=$(tail -n +2 "$CSV_FILE" | cut -d, -f2 | sort -u)
    
    for filepath in $previous_files; do
        # Skip if file no longer exists
        if [[ ! -f "$filepath" ]]; then
            continue
        fi
        
        # Get current file size
        local current_size=$(stat -c %s "$filepath" 2>/dev/null)
        
        # Skip if we couldn't get the size
        if [[ -z "$current_size" ]]; then
            continue
        fi
        
        # Get mount point
        local mount_point=$(df --output=target "$filepath" | tail -1)
        
        # Get previous size
        local previous_size=$(grep ",$filepath," "$CSV_FILE" | tail -1 | cut -d, -f3)
        
        # Save current data
        save_file_data "$filepath" "$current_size" "$mount_point" "rescan"
        ((rescan_count++))
        
        # Check for growth
        if [[ -n "$previous_size" && "$current_size" -gt "$previous_size" ]]; then
            local growth=$((current_size - previous_size))
            local growth_mb=$((growth / 1024 / 1024))
            
            if [[ $growth_mb -ge $GROWTH_PATTERN_THRESHOLD_MB ]]; then
                log_message "WARNING" "File growth detected: $filepath (grew by $(format_size $growth))"
                ((growth_count++))
                
                # Update consecutive growth counter
                local key="consecutive_growth_$filepath"
                local current_count=$(get_state_value "$key" "0")
                set_state_value "$key" "$((current_count + 1))"
            fi
        fi
        
        # Show progress in interactive mode
        if [[ "$INTERACTIVE_MODE" == true && "$PROGRESS_INDICATORS" == true ]]; then
            if ((rescan_count % 10 == 0)); then
                echo -n "."
            fi
        fi
    done
    
    if [[ "$INTERACTIVE_MODE" == true && "$PROGRESS_INDICATORS" == true ]]; then
        echo ""  # New line after progress dots
    fi
    
    # Update state file
    set_state_value "last_scan_timestamp" "$(date -Iseconds)"
    
    log_message "SUCCESS" "Rescan completed. Checked $rescan_count files, found $growth_count with significant growth."
    
    return 0
}

#=============================================================================
# ANALYSIS FUNCTIONS
#=============================================================================

# Check for low disk space
check_disk_space() {
    log_message "INFO" "Checking for low disk space"
    
    local low_space_count=0
    local filesystems=$(get_mounted_filesystems)
    
    for fs in $filesystems; do
        # Skip excluded filesystems
        if is_excluded_filesystem "$fs"; then
            continue
        fi
        
        # Get available space in GB
        local avail_kb=$(df --output=avail "$fs" | tail -1)
        local avail_gb=$((avail_kb / 1024 / 1024))
        
        if [[ $avail_gb -lt $DISK_SPACE_ALERT_GB ]]; then
            log_message "WARNING" "Low disk space on $fs: $avail_gb GB available"
            ((low_space_count++))
            
            # Check if we should send an alert (respect spam prevention)
            local last_alert_key="last_email_low_space_${fs//\//_}"
            local last_alert_time=$(get_state_value "$last_alert_key" "")
            
            if [[ -z "$last_alert_time" ]]; then
                # No previous alert, send one
                send_low_space_alert "$fs" "$avail_gb"
                set_state_value "$last_alert_key" "$(date -Iseconds)"
            else
                # Check if enough time has passed since last alert
                local last_alert_epoch=$(date -d "$last_alert_time" +%s)
                local current_epoch=$(date +%s)
                local hours_diff=$(( (current_epoch - last_alert_epoch) / 3600 ))
                
                if [[ $hours_diff -ge $EMAIL_SPAM_PREVENTION_HOURS ]]; then
                    send_low_space_alert "$fs" "$avail_gb"
                    set_state_value